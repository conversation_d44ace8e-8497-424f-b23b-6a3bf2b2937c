/**
 * 告警发送数据管理Hook
 * 管理告警发送列表的加载、搜索、分页等状态
 */

import { useState, useCallback, useEffect } from 'react';
import { App } from 'antd';
import type { AlertSend, AlertSendSearchParams } from '../types';
import { DEFAULT_PAGINATION } from '../constants';
import { AlertSendService } from '../services/alertSend';

export interface UseAlertSendDataOptions {
  /** 是否自动加载数据 */
  autoLoad?: boolean;
  /** 初始搜索参数 */
  initialParams?: AlertSendSearchParams;
}

export interface UseAlertSendDataReturn {
  /** 告警发送列表数据 */
  data: AlertSend[];
  /** 数据加载状态 */
  loading: boolean;
  /** 数据总数 */
  total: number;
  /** 分页状态 */
  pagination: {
    current: number;
    page_size: number;
  };
  /** 搜索参数 */
  searchParams: AlertSendSearchParams;
  /** 加载数据方法 */
  loadData: (customParams?: Partial<AlertSendSearchParams>) => Promise<void>;
  /** 刷新数据方法 */
  refreshData: () => Promise<void>;
  /** 重置数据方法 */
  resetData: () => void;
  /** 更新搜索参数方法 */
  updateSearchParams: (params: AlertSendSearchParams) => void;
  /** 更新分页方法 */
  updatePagination: (current: number, pageSize: number) => void;
}

/**
 * 告警发送数据管理Hook
 */
export function useAlertSendData(options: UseAlertSendDataOptions = {}): UseAlertSendDataReturn {
  const { autoLoad = true, initialParams = {} } = options;
  const { message } = App.useApp();

  // 状态管理
  const [data, setData] = useState<AlertSend[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState(DEFAULT_PAGINATION);
  const [searchParams, setSearchParams] = useState<AlertSendSearchParams>(initialParams);

  // 加载数据方法
  const loadData = useCallback(
    async (customParams?: Partial<AlertSendSearchParams>) => {
      setLoading(true);
      try {
        // 合并搜索参数
        const finalParams = {
          ...searchParams,
          ...pagination,
          ...customParams,
        };

        console.log('加载告警发送数据，参数:', finalParams);

        // 调用API获取数据
        const response = await AlertSendService.getAlertSends(finalParams);
        
        if (!response || !response.data || response.data == null) {
          console.warn('获取告警发送数据失败：返回数据为空');
          message.warning('未获取到告警发送数据');
          setData([]);
          setTotal(0);
          return;
        }
        // 模拟分页
        const pageSize = finalParams.page_size || DEFAULT_PAGINATION.page_size;
        const current = finalParams.current || DEFAULT_PAGINATION.current;

        setData(response.data);
        setTotal(response.total);
        
        // 更新分页状态
        setPagination({
          current,
          page_size: pageSize,
        });

        console.log('告警发送数据加载成功:', response);
      } catch (error) {
        console.error('加载告警发送数据失败:', error);
        message.error('加载数据失败');
        setData([]);
        setTotal(0);
      } finally {
        setLoading(false);
      }
    },
    [searchParams, pagination, message]
  );

  // 刷新数据方法
  const refreshData = useCallback(async () => {
    await loadData();
  }, [loadData]);

  // 重置数据方法
  const resetData = useCallback(() => {
    setSearchParams({});
    setPagination(DEFAULT_PAGINATION);
    setData([]);
    setTotal(0);
  }, []);

  // 更新搜索参数方法
  const updateSearchParams = useCallback((params: AlertSendSearchParams) => {
    setSearchParams(params);
  }, []);

  // 更新分页方法
  const updatePagination = useCallback((current: number, pageSize: number) => {
    setPagination({ current, page_size: pageSize });
  }, []);

  // 自动加载数据
  useEffect(() => {
    if (autoLoad) {
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoLoad]);

  return {
    data,
    loading,
    total,
    pagination,
    searchParams,
    loadData,
    refreshData,
    resetData,
    updateSearchParams,
    updatePagination,
  };
}
